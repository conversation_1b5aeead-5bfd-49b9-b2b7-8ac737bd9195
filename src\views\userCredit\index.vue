<template>
  <div class="user-credit-container">
    <!-- 顶部导航栏 -->
    <div class="nav-header" v-if="hasToken">
      <div class="nav-title" @click="handleTitleClick">话费充值</div>
      <div class="nav-subtitle">快速便捷的话费充值服务</div>
    </div>

    <!-- 未登录提示 -->
    <div v-if="!hasToken" class="login-required-container">
      <div class="login-required-content">
        <i class="el-icon-warning-outline"></i>
        <h3>请登录</h3>
        <p>请通过小程序访问此页面</p>
      </div>
    </div>

    <!-- 功能内容区域（有token时显示） -->
    <div v-if="hasToken">
      <!-- 用户信息表单 -->
      <div class="form-section">
        <div class="form-title">
          <i class="el-icon-user"></i>
          <span>充值信息</span>
        </div>

        <el-form :model="formData" :rules="rules" ref="creditForm" label-width="100px">
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="formData.phone" placeholder="请输入手机号" maxlength="11" type="number" clearable>
              <i slot="prefix" class="el-icon-phone"></i>
            </el-input>
          </el-form-item>

          <el-form-item label="真实姓名" prop="name">
            <el-input v-model="formData.name" placeholder="请输入手机号绑定姓名" maxlength="20" clearable>
              <i slot="prefix" class="el-icon-user"></i>
            </el-input>
          </el-form-item>
        </el-form>
      </div>

      <!-- 移动联通套餐选择 -->
      <div class="package-section">
        <div class="package-title">
          <i class="el-icon-phone"></i>
          <span>移动联通话费充值</span>
        </div>
        <div class="package-grid">
          <div v-for="(item, index) in creditPackage.creditGoods1" :key="index" class="package-item"
            :class="{ 'package-active': item.id === activeCredit.id }" @click="selectCreditItem(item)">
            <el-image :src="item.image" fit="cover" class="package-image" :preview-src-list="[item.image]" />
            <div v-if="item.id === activeCredit.id" class="package-overlay">
              <i class="el-icon-check"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 电信套餐选择 -->
      <div class="package-section">
        <div class="package-title">
          <i class="el-icon-phone" style="color: #ff6b35"></i>
          <span>电信充值通道专用</span>
        </div>
        <div class="package-grid">
          <div v-for="(item, index) in creditPackage.creditGoods2" :key="index" class="package-item"
            :class="{ 'package-active': item.id === activeCredit.id }" @click="selectCreditItem(item)">
            <el-image :src="item.image" fit="cover" class="package-image" :preview-src-list="[item.image]" />
            <div v-if="item.id === activeCredit.id" class="package-overlay">
              <i class="el-icon-check"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 套餐详情 -->
      <div class="details-section">
        <div class="details-title">
          <i class="el-icon-info"></i>
          <span>套餐详情</span>
        </div>

        <div class="details-card">
          <div class="details-header">
            <el-image :src="activeCredit.image" fit="cover" class="details-image"
              :preview-src-list="[activeCredit.image]" />
            <div class="details-info">
              <div class="product-name">{{ activeCredit.title }}</div>
              <div class="product-spec">规格：{{ activeCredit.facePrice }}</div>
              <div class="product-price">¥{{ activeCredit.userPay }}</div>
            </div>
          </div>

          <el-divider></el-divider>

          <div class="details-list">
            <div class="detail-item">
              <div class="detail-label">
                <i class="el-icon-truck"></i>
                <span>配送方式</span>
              </div>
              <el-tag type="success" size="small">自动发货</el-tag>
            </div>

            <div class="detail-item">
              <div class="detail-label">
                <i class="el-icon-money"></i>
                <span>商品金额</span>
              </div>
              <div class="detail-value">¥{{ activeCredit.userPay }}</div>
            </div>

            <div class="detail-item">
              <div class="detail-label">
                <i class="el-icon-truck"></i>
                <span>运费</span>
              </div>
              <div class="detail-value free">免费</div>
            </div>

            <div class="detail-item">
              <div class="detail-label">
                <i class="el-icon-present"></i>
                <span>抵扣券</span>
              </div>
              <div class="detail-value disabled">无可用抵扣券</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 余额抵扣 -->
      <div class="balance-section">
        <div class="balance-card">
          <i class="el-icon-wallet"></i>
          <div class="balance-text">
            余额可抵
            <span class="balance-amount">{{ activeCredit.integral }}</span> 元
          </div>
        </div>
      </div>

      <!-- 底部操作栏 -->
      <div class="footer-fixed">
        <div class="footer-content">
          <div class="footer-left">
            <span class="total-label">总计：</span>
            <span class="total-price">¥{{ activeCredit.userPay }}</span>
          </div>
          <el-button type="primary" @click="showWarningDialog" :loading="paymentLoading" class="submit-btn">
            <i class="el-icon-shopping-cart-2"></i>
            提交订单
          </el-button>
        </div>
      </div>

      <!-- 温馨提示弹窗 -->
      <el-dialog title="充值温馨提示" :visible.sync="warningDialogVisible" width="80%" :close-on-click-modal="false"
        class="warning-dialog">
        <div class="warning-content">
          <div class="warning-section">
            <h4><i class="el-icon-lock"></i> 充值须知</h4>

            <div class="warning-item important">
              <span class="warning-number">1</span>
              <div class="warning-text">
                <strong>充值安全:</strong>
                本次充值未到账前。严禁同一号码在任何平台再次下单，如果有下单无售后。
              </div>
            </div>

            <div class="warning-item">
              <span class="warning-number">2</span>
              <div class="warning-text">
                <strong>防范诈骗:</strong>
                我们不会主动致电，如遇陌生来电需警惕。
              </div>
            </div>

            <div class="warning-item">
              <span class="warning-number">3</span>
              <div class="warning-text">
                <strong>客户支持:</strong>
                如有疑问，请通过官方平台联系在线客服。
              </div>
            </div>

            <div class="general-tip">
              请严格遵守以上安全准则，共同维护信息安全。如需帮助，随时联系我们。
            </div>
          </div>

          <div class="warning-section">
            <h4><i class="el-icon-time"></i> 充值说明</h4>

            <div class="warning-item">
              <span class="warning-number">4</span>
              <div class="warning-text">
                充值到账时间为0-48小时，建议月中充值，因为月初月尾会有延迟（不超过72小时），介意勿拍！
              </div>
            </div>

            <div class="warning-item important">
              <span class="warning-number">5</span>
              <div class="warning-text">
                <strong>[充值失败]</strong>
                网络充值存在10%-20%的失败率充值失败订单会在未来24小时内款项会原路退回，请注意查收;
              </div>
            </div>

            <div class="warning-item important">
              <span class="warning-number">6</span>
              <div class="warning-text">
                携号转网，充值号码错误，空号，虚商号码，企业号，号码停机，未实名号码勿拍，因为这些原因导致的充值失败，概不负责。
              </div>
            </div>

            <div class="warning-item important">
              <span class="warning-number">7</span>
              <div class="warning-text">
                不支持充值后撤销订单，充值之后等待到账即可。
              </div>
            </div>
          </div>

          <div class="warning-section">
            <h4><i class="el-icon-warning"></i> 注意⚠️</h4>

            <div class="telecom-warning">
              电信手机号请月中进行充值！月初月尾充值会非常慢到账！介意慎拍！！！电信已更新支付13个地区：
              广东， 江苏， 河北， 江西， 河南， 甘肃， 湖北， 四川， 福建，
              吉林， 辽宁， 山东， 贵州 开启时间为每日7: 30-晚22: 00)
              其他地区在更新中 移动、联通支持全国！
            </div>
          </div>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="warningDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmPayment" :disabled="countdown > 0" :loading="paymentLoading">
            {{ countdown > 0 ? `${countdown}s` : '确定' }}
          </el-button>
        </div>
      </el-dialog>

      <!-- 加载遮罩 -->
      <div v-loading="loading" element-loading-text="加载中..."></div>



    </div>

    <!-- 调试日志弹窗 -->
    <el-dialog title="调试日志" :visible.sync="debugLogVisible" width="90%" top="5vh">
      <div class="debug-log-content">
        <div v-for="(log, index) in debugLogs" :key="index" class="log-item" :class="log.type">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="clearDebugLog">清空日志</el-button>
        <el-button @click="copyDebugLog" type="success">复制日志</el-button>
        <el-button type="primary" @click="debugLogVisible = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getCreditGoods,
} from "@/api/creditGoods";
import {
  createCreditPayment,
} from "@/api/credit_order";

export default {
  name: "UserCredit",
  data() {
    return {
      loading: false,
      paymentLoading: false,
      warningDialogVisible: false,
      countdown: 0,
      timer: null,
      hasToken: false, // 是否有token（用于控制页面显示）
      formData: {
        phone: "",
        name: "",
      },
      rules: {
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          {
            pattern: /^(1[3-9]\d{9})$|^([69]\d{7})$|^([6]\d{5})$|^(09\d{8})$/,
            message: "手机号格式错误",
            trigger: "blur",
          },
        ],
        name: [
          { required: true, message: "请输入真实姓名", trigger: "blur" },
        ],
      },
      creditPackage: {
        creditGoods1: [],
        creditGoods2: [],
      },
      activeCredit: {},
      isInMiniProgram: false, // 是否在小程序webview环境中
      debugLogVisible: false, // 调试日志弹窗显示状态
      debugLogs: [], // 调试日志数组
      titleClickCount: 0, // 标题点击次数
      titleClickTimer: null, // 标题点击计时器
    };
  },
  mounted() {
    this.addDebugLog('info', '页面初始化开始');
    this.loadCreditGoods();
    this.getUserToken();
    this.initWxMiniProgram();
    this.addDebugLog('success', '页面初始化完成');
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.titleClickTimer) {
      clearTimeout(this.titleClickTimer);
    }
  },
  methods: {
    // 获取用户token (从URL参数或localStorage)
    getUserToken() {
      const token = this.getUrlParam('token');
      const userId = this.getUrlParam('userId');
      const source = this.getUrlParam('source');

      console.log('获取到的URL参数:', { token, userId, source });

      if (token) {
        // 设置hasToken为true，显示功能内容
        this.hasToken = true;
        // 设置到localStorage供axios拦截器使用
        localStorage.setItem('userToken', token);
        // 同时设置到cookies供PC端auth.js使用
        this.setTokenToCookie(token);
        console.log('Token已设置到localStorage和cookies:', token);
      } else {
        // 没有token，设置hasToken为false，显示登录提示
        this.hasToken = false;
        console.log('未检测到token，显示登录提示');
      }

      if (userId) {
        localStorage.setItem('userId', userId);
        this.setUserIdToCookie(userId);
        console.log('UserId已设置:', userId);
      }

      // 如果是从小程序来的，显示提示信息
      if (source === 'miniprogram') {
        console.log('检测到来自小程序的访问');
      }
    },

    // 兼容hash路由和普通路由的URL参数获取方法
    getUrlParam(name) {
      // 首先尝试从hash后的参数获取（适用于hash路由）
      const hash = window.location.hash;
      if (hash && hash.includes('?')) {
        const hashParams = hash.split('?')[1];
        const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
        const r = hashParams.match(reg);
        if (r != null) return decodeURIComponent(r[2]);
      }

      // 然后尝试从search参数获取（适用于普通路由）
      const search = window.location.search.substring(1);
      if (search) {
        const reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)');
        const r = search.match(reg);
        if (r != null) return decodeURIComponent(r[2]);
      }

      return null;
    },

    // 设置token到cookies
    setTokenToCookie(token) {
      // 使用与PC端auth.js相同的方式设置cookie
      document.cookie = `token=${token}; path=/; max-age=86400`; // 24小时过期
    },

    // 设置userId到cookies
    setUserIdToCookie(userId) {
      document.cookie = `userid=${userId}; path=/; max-age=86400`; // 24小时过期
    },

    // 初始化微信小程序环境
    initWxMiniProgram() {
      // 检测是否在微信小程序webview环境中
      if (typeof wx !== 'undefined' && wx.miniProgram) {
        this.addDebugLog('success', '检测到微信小程序环境');
        this.isInMiniProgram = true;

        // 初始化微信小程序JSSDK
        wx.miniProgram.getEnv((res) => {
          this.addDebugLog('info', `小程序环境信息: ${JSON.stringify(res)}`);
          if (res.miniprogram) {
            this.addDebugLog('success', '确认在小程序webview中');
          }
        });
      } else {
        this.addDebugLog('warn', '非小程序环境');
        this.isInMiniProgram = false;
      }
    },

    // 加载话费商品
    async loadCreditGoods() {
      this.loading = true;
      try {
        const response = await getCreditGoods();
        if (response.data && response.data.data) {
          this.creditPackage = response.data.data;
          if (this.creditPackage.creditGoods1.length > 0) {
            this.activeCredit = this.creditPackage.creditGoods1[0];
          }
        }
      } catch (error) {
        this.$message.error("加载话费商品失败");
        console.error(error);
      } finally {
        this.loading = false;
      }
    },

    // 选择话费商品
    selectCreditItem(item) {
      this.activeCredit = item;
    },

    // 显示警告弹窗
    showWarningDialog() {
      this.$refs.creditForm.validate((valid) => {
        if (valid) {
          this.warningDialogVisible = true;
          this.startCountdown();
        }
      });
    },

    // 开始倒计时
    startCountdown() {
      this.countdown = 3;
      this.timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(this.timer);
        }
      }, 1000);
    },

    // 确认支付
    async confirmPayment() {
      this.paymentLoading = true;
      this.addDebugLog('info', '开始确认支付');

      try {
        const token = localStorage.getItem('userToken');
        if (!token) {
          this.addDebugLog('error', '未找到用户token');
          this.$message.error("请先登录");
          return;
        }

        const paymentData = {
          userId: this.getUserIdFromToken(token),
          id: this.activeCredit.cerditId,
          type: "2",
          phone: this.formData.phone,
          people_name: this.formData.name,
        };

        this.addDebugLog('info', `发送支付请求: ${JSON.stringify(paymentData)}`);
        const response = await createCreditPayment(paymentData);

        this.addDebugLog('info', `支付响应: ${JSON.stringify(response.data)}`);

        // 修复：后端返回的code是数字200，不是字符串"200"
        if (response.data.code === 200 || response.data.code === "200") {
          this.addDebugLog('success', '支付请求成功，开始处理支付数据');
          this.handlePaymentSuccess(response.data);
        } else {
          this.addDebugLog('error', `支付请求失败: ${response.data.message}`);
          this.$message.error(response.data.message || "支付请求失败");
        }
      } catch (error) {
        this.$message.error("支付请求失败");
        console.error(error);
      } finally {
        this.paymentLoading = false;
        this.warningDialogVisible = false;
      }
    },

    // 处理支付成功
    handlePaymentSuccess(paymentData) {
      this.addDebugLog('info', `支付数据处理: ${JSON.stringify(paymentData)}`);

      // 在webview环境中，通知小程序进行支付
      if (this.isInMiniProgram && window.wx && window.wx.miniProgram) {
        this.addDebugLog('success', `向小程序发送支付数据: ${JSON.stringify(paymentData.data)}`);

        // 发送支付数据给小程序
        window.wx.miniProgram.postMessage({
          data: {
            type: 'payment',
            paymentData: paymentData.data
          }
        });

        this.addDebugLog('info', 'postMessage已发送，准备跳转');

        // 可选：不立即返回，让用户看到支付信息
        this.$message.success("正在跳转到支付页面...");

        // 延迟一下再跳转，让用户看到提示
        setTimeout(() => {
          this.addDebugLog('info', '开始navigateBack');
          window.wx.miniProgram.navigateBack();
        }, 1000);

      } else {
        // 非小程序环境，显示支付信息或进行其他处理
        this.addDebugLog('warn', '非小程序环境，显示支付信息');
        this.$message.success("支付信息获取成功");

        // 在非小程序环境中，可以显示支付二维码或其他支付方式
        this.showPaymentInfo(paymentData);
      }
    },

    // 显示支付信息（非小程序环境）
    showPaymentInfo(paymentData) {
      // 这里可以显示支付二维码或其他支付信息
      console.log('非小程序环境支付信息:', paymentData);
      // 可以弹出对话框显示支付信息
      this.$alert(`支付信息已生成，请使用微信扫码支付`, '支付信息', {
        confirmButtonText: '确定'
      });
    },

    // 从token中获取用户ID (简单实现，实际应该解析JWT)
    getUserIdFromToken(token) {
      // 这里应该实现真正的token解析逻辑
      // 暂时返回一个默认值或从其他地方获取
      this.addDebugLog('info', `获取用户ID，token: ${token ? '存在' : '不存在'}`);
      return localStorage.getItem('userId') || '1';
    },

    // 添加调试日志
    addDebugLog(type, message) {
      const log = {
        type: type, // info, warn, error, success
        message: message,
        time: new Date().toLocaleTimeString()
      };
      this.debugLogs.unshift(log); // 新日志添加到顶部

      // 限制日志数量，避免内存占用过多
      if (this.debugLogs.length > 100) {
        this.debugLogs = this.debugLogs.slice(0, 100);
      }

      // 同时输出到console
      console.log(`[${type.toUpperCase()}] ${message}`);
    },

    // 显示调试日志
    showDebugLog() {
      this.debugLogVisible = true;
    },

    // 清空调试日志
    clearDebugLog() {
      this.debugLogs = [];
      this.addDebugLog('info', '日志已清空');
    },

    // 复制调试日志
    copyDebugLog() {
      if (this.debugLogs.length === 0) {
        this.$message.warning('暂无日志可复制');
        return;
      }

      // 格式化日志内容
      const logContent = this.debugLogs.map(log => {
        return `[${log.time}] [${log.type.toUpperCase()}] ${log.message}`;
      }).join('\n');

      // 添加页面信息头部
      const pageInfo = `=== 话费充值页面调试日志 ===\n生成时间: ${new Date().toLocaleString()}\n日志条数: ${this.debugLogs.length}\n\n`;
      const fullContent = pageInfo + logContent;

      // 复制到剪贴板
      this.copyToClipboard(fullContent);
    },

    // 复制文本到剪贴板
    copyToClipboard(text) {
      // 创建临时文本域
      const textArea = document.createElement('textarea');
      textArea.value = text;
      textArea.style.position = 'fixed';
      textArea.style.left = '-999999px';
      textArea.style.top = '-999999px';
      document.body.appendChild(textArea);

      try {
        // 选择并复制文本
        textArea.focus();
        textArea.select();
        const successful = document.execCommand('copy');

        if (successful) {
          this.$message.success('日志已复制到剪贴板');
          this.addDebugLog('success', '日志复制成功');
        } else {
          throw new Error('复制命令执行失败');
        }
      } catch (err) {
        console.error('复制失败:', err);
        this.$message.error('复制失败，请手动选择复制');
        this.addDebugLog('error', `日志复制失败: ${err.message}`);
      } finally {
        // 清理临时元素
        document.body.removeChild(textArea);
      }
    },

    // 处理标题点击事件
    handleTitleClick() {
      this.titleClickCount++;
      this.addDebugLog('info', `标题点击次数: ${this.titleClickCount}`);

      // 清除之前的计时器
      if (this.titleClickTimer) {
        clearTimeout(this.titleClickTimer);
      }

      // 如果达到8次点击，显示调试日志
      if (this.titleClickCount >= 8) {
        this.showDebugLog();
        this.titleClickCount = 0; // 重置计数器
        this.addDebugLog('success', '调试模式已激活');
        return;
      }

      // 设置3秒后重置计数器
      this.titleClickTimer = setTimeout(() => {
        if (this.titleClickCount > 0) {
          this.addDebugLog('info', '点击计数器已重置');
        }
        this.titleClickCount = 0;
      }, 3000);
    },
  },
};
</script>

<style lang="scss" scoped>
.user-credit-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #f8f9fa 0%, #ededed 100%);
  padding-bottom: 120px;
}

/* 顶部导航栏 */
.nav-header {
  background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
  padding: 40px 20px 30px;
  color: white;
  text-align: center;

  .nav-title {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
    letter-spacing: 0.5px;
  }

  .nav-subtitle {
    font-size: 14px;
    opacity: 0.9;
    font-weight: 400;
  }
}

/* 表单区域 */
.form-section {
  margin: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);

  .form-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    font-size: 16px;
    font-weight: 600;
    color: #333;

    i {
      margin-right: 8px;
      color: #00d4aa;
    }
  }
}

/* 套餐选择区域 */
.package-section {
  margin: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);

  .package-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #333;

    i {
      margin-right: 8px;
      color: #00d4aa;
    }
  }

  .package-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;

    .package-item {
      position: relative;
      border-radius: 12px;
      overflow: hidden;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      cursor: pointer;

      &.package-active {
        border-color: #00d4aa;
        transform: scale(1.05);
        box-shadow: 0 4px 16px rgba(0, 212, 170, 0.3);
      }

      .package-image {
        width: 100%;
        height: 80px;
        border-radius: 10px;
      }

      .package-overlay {
        position: absolute;
        top: 0;
        right: 0;
        background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
        width: 24px;
        height: 24px;
        border-radius: 0 10px 0 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
      }
    }
  }
}

/* 详情区域 */
.details-section {
  margin: 20px;
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 212, 170, 0.08);

  .details-title {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 600;
    color: #333;

    i {
      margin-right: 8px;
      color: #00d4aa;
    }
  }

  .details-card {
    .details-header {
      display: flex;
      align-items: center;
      padding-bottom: 16px;

      .details-image {
        width: 80px;
        height: 80px;
        border-radius: 12px;
        margin-right: 16px;
        border: 1px solid #f0f0f0;
      }

      .details-info {
        flex: 1;

        .product-name {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 6px;
        }

        .product-spec {
          font-size: 13px;
          color: #999;
          margin-bottom: 8px;
        }

        .product-price {
          font-size: 18px;
          font-weight: 600;
          color: #00d4aa;
        }
      }
    }

    .details-list {
      .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 0;

        .detail-label {
          display: flex;
          align-items: center;
          font-size: 14px;
          color: #666;

          i {
            margin-right: 6px;
          }
        }

        .detail-value {
          font-size: 14px;
          color: #333;
          font-weight: 500;

          &.free {
            color: #00d4aa;
          }

          &.disabled {
            color: #999;
          }
        }
      }
    }
  }
}

/* 余额区域 */
.balance-section {
  margin: 20px;

  .balance-card {
    background: linear-gradient(135deg, rgba(0, 212, 170, 0.1) 0%, rgba(0, 163, 137, 0.1) 100%);
    border: 1px solid rgba(0, 212, 170, 0.2);
    border-radius: 16px;
    padding: 16px 20px;
    display: flex;
    align-items: center;

    i {
      color: #00d4aa;
      font-size: 18px;
      margin-right: 12px;
    }

    .balance-text {
      font-size: 14px;
      color: #333;
      letter-spacing: 0.5px;

      .balance-amount {
        color: #fa514a;
        font-size: 16px;
        font-weight: 600;
        margin: 0 4px;
      }
    }
  }
}

/* 底部操作栏 */
.footer-fixed {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;

  .footer-content {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .footer-left {
      display: flex;
      align-items: baseline;

      .total-label {
        font-size: 14px;
        color: #666;
        margin-right: 8px;
      }

      .total-price {
        font-size: 20px;
        font-weight: 600;
        color: #00d4aa;
      }
    }

    .submit-btn {
      width: 100%;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
      font-weight: 600;
      background: linear-gradient(135deg, #00d4aa 0%, #00a389 100%);
      border: none;
      box-shadow: 0 4px 12px rgba(0, 212, 170, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(0, 212, 170, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      i {
        margin-right: 6px;
      }
    }
  }
}

/* 警告弹窗样式 */
.warning-dialog {
  .warning-content {
    max-height: 400px;
    overflow-y: auto;

    .warning-section {
      h4 {
        display: flex;
        align-items: center;
        color: #333;
        margin-bottom: 15px;

        i {
          margin-right: 8px;
          color: #00d4aa;
        }
      }

      .warning-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 15px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 3px solid #e9ecef;

        &.important {
          background: #fff7ed;
          border-left-color: #f97316;
        }

        .warning-number {
          min-width: 20px;
          height: 20px;
          background: #00d4aa;
          color: white;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          font-weight: 600;
          margin-top: 2px;
        }

        .warning-text {
          flex: 1;
          line-height: 1.6;
          font-size: 14px;
          color: #333;

          strong {
            color: #f97316;
          }
        }
      }

      .general-tip {
        background: #f0f9ff;
        border: 1px solid #e0f2fe;
        border-radius: 8px;
        padding: 12px;
        font-size: 14px;
        color: #0369a1;
        line-height: 1.6;
        margin-top: 15px;
      }

      .telecom-warning {
        background: #fff7ed;
        border: 1px solid #fed7aa;
        border-radius: 8px;
        padding: 15px;
        font-size: 14px;
        color: #ea580c;
        line-height: 1.6;
        font-weight: 500;
      }
    }
  }
}



.debug-log-content {
  max-height: 400px;
  overflow-y: auto;
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin-bottom: 8px;
  padding: 5px 8px;
  border-radius: 3px;
  font-size: 12px;
  line-height: 1.4;
}

.log-item.info {
  background: #e1f5fe;
  color: #01579b;
}

.log-item.success {
  background: #e8f5e8;
  color: #2e7d32;
}

.log-item.warn {
  background: #fff3e0;
  color: #ef6c00;
}

.log-item.error {
  background: #ffebee;
  color: #c62828;
}

.log-time {
  font-weight: bold;
  margin-right: 8px;
}

.log-message {
  word-break: break-all;
}

/* 登录提示样式 */
.login-required-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 40px 20px;
}

.login-required-content {
  text-align: center;
  background: white;
  border-radius: 16px;
  padding: 60px 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  max-width: 400px;
  width: 100%;
}

.login-required-content i {
  font-size: 64px;
  color: #f56c6c;
  margin-bottom: 20px;
  display: block;
}

.login-required-content h3 {
  font-size: 24px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 600;
}

.login-required-content p {
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .package-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .footer-content {
    flex-direction: column;
    gap: 10px;

    .submit-btn {
      width: 100%;
      height: 48px;
    }
  }

  .login-required-content {
    padding: 40px 20px;
  }

  .login-required-content i {
    font-size: 48px;
  }

  .login-required-content h3 {
    font-size: 20px;
  }

  .login-required-content p {
    font-size: 14px;
  }
}
</style>
